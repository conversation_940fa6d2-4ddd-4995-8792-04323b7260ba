import { createTRPCRouter } from "./init";
import { authRouter } from "./routers/auth";
import { petsRouter } from "./routers/pets";
import { contentRouter } from "./routers/content";
import { progressRouter } from "./routers/progress";
import { gamificationRouter } from "./routers/gamification";

/**
 * This is the primary router for your server.
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
  auth: authRouter,
  pets: petsRouter,
  content: contentRouter,
  progress: progressRouter,
  gamification: gamificationRouter,
});

// Export type definition of API
export type AppRouter = typeof appRouter;
