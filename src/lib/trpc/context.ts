import { type CreateNextContextOptions } from "@trpc/server/adapters/next";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";

/**
 * Creates context for tRPC requests
 * This runs for every tRPC request and provides:
 * - Database instance
 * - Current user session (if authenticated)
 * - Request/response objects
 */
export async function createTRPCContext(opts: CreateNextContextOptions) {
  const { req, res } = opts;

  // Get the session from the request
  const session = await auth.api.getSession({
    headers: req.headers,
  });

  return {
    db,
    session,
    req,
    res,
  };
}

export type Context = Awaited<ReturnType<typeof createTRPCContext>>;
