import { z } from "zod";
import { createTR<PERSON>Router, publicProcedure, protectedProcedure } from "../init";
import { TRPCError } from "@trpc/server";
import { pets, userPets, insertUserPetSchema } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";

export const petsRouter = createTRPCRouter({
  // Get all available pets
  getAvailablePets: publicProcedure.query(async ({ ctx }) => {
    return await ctx.db
      .select()
      .from(pets)
      .where(eq(pets.isActive, true))
      .orderBy(pets.name);
  }),

  // Get user's selected pet
  getUserPet: protectedProcedure.query(async ({ ctx }) => {
    const userPet = await ctx.db
      .select({
        id: userPets.id,
        petName: userPets.petName,
        selectedAt: userPets.selectedAt,
        pet: {
          id: pets.id,
          name: pets.name,
          type: pets.type,
          imageUrl: pets.imageUrl,
          description: pets.description,
          personality: pets.personality,
        },
      })
      .from(userPets)
      .innerJoin(pets, eq(userPets.petId, pets.id))
      .where(
        and(
          eq(userPets.userId, ctx.session.user.id),
          eq(userPets.isActive, true)
        )
      )
      .limit(1);

    return userPet[0] || null;
  }),

  // Select a pet for the user
  selectPet: protectedProcedure
    .input(
      z.object({
        petId: z.string().uuid(),
        petName: z.string().min(1).max(100),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Check if pet exists
      const pet = await ctx.db
        .select()
        .from(pets)
        .where(eq(pets.id, input.petId))
        .limit(1);

      if (pet.length === 0) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Pet not found",
        });
      }

      // Check if user already has a pet selected
      const existingUserPet = await ctx.db
        .select()
        .from(userPets)
        .where(
          and(
            eq(userPets.userId, ctx.session.user.id),
            eq(userPets.isActive, true)
          )
        )
        .limit(1);

      if (existingUserPet.length > 0) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "User already has a pet selected",
        });
      }

      // Create user pet selection
      const [newUserPet] = await ctx.db
        .insert(userPets)
        .values({
          userId: ctx.session.user.id,
          petId: input.petId,
          petName: input.petName,
        })
        .returning();

      return newUserPet;
    }),

  // Update pet name
  updatePetName: protectedProcedure
    .input(
      z.object({
        petName: z.string().min(1).max(100),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const [updatedUserPet] = await ctx.db
        .update(userPets)
        .set({
          petName: input.petName,
        })
        .where(
          and(
            eq(userPets.userId, ctx.session.user.id),
            eq(userPets.isActive, true)
          )
        )
        .returning();

      if (!updatedUserPet) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "User pet not found",
        });
      }

      return updatedUserPet;
    }),

  // Get pet personality for AI interactions (future feature)
  getPetPersonality: protectedProcedure.query(async ({ ctx }) => {
    const userPet = await ctx.db
      .select({
        personality: pets.personality,
        petName: userPets.petName,
        petType: pets.type,
      })
      .from(userPets)
      .innerJoin(pets, eq(userPets.petId, pets.id))
      .where(
        and(
          eq(userPets.userId, ctx.session.user.id),
          eq(userPets.isActive, true)
        )
      )
      .limit(1);

    if (userPet.length === 0) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "User pet not found",
      });
    }

    return userPet[0];
  }),
});
