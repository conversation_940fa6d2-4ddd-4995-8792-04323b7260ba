import { z } from "zod";
import { createTR<PERSON>Router, publicProcedure, protectedProcedure } from "../init";
import { TRPCError } from "@trpc/server";
import bcrypt from "bcryptjs";
import { users, userProfiles, insertUserSchema, insertUserProfileSchema } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

export const authRouter = createTRPCRouter({
  // Register new user
  register: publicProcedure
    .input(
      z.object({
        email: z.string().email(),
        password: z.string().min(8),
        fullName: z.string().min(2),
        standard: z.enum(["8th", "9th", "10th"]),
        schoolName: z.string().min(2),
        educationalBoard: z.enum(["CBSE", "ICSE", "State Board"]),
        interests: z.string().optional(),
        dislikes: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Check if user already exists
      const existingUser = await ctx.db
        .select()
        .from(users)
        .where(eq(users.email, input.email))
        .limit(1);

      if (existingUser.length > 0) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "User with this email already exists",
        });
      }

      // Hash password
      const passwordHash = await bcrypt.hash(input.password, 12);

      // Create user
      const [newUser] = await ctx.db
        .insert(users)
        .values({
          email: input.email,
          passwordHash,
        })
        .returning();

      // Create user profile
      const [newProfile] = await ctx.db
        .insert(userProfiles)
        .values({
          userId: newUser.id,
          fullName: input.fullName,
          standard: input.standard,
          schoolName: input.schoolName,
          educationalBoard: input.educationalBoard,
          interests: input.interests,
          dislikes: input.dislikes,
          profileCompleted: true,
        })
        .returning();

      return {
        user: {
          id: newUser.id,
          email: newUser.email,
        },
        profile: newProfile,
      };
    }),

  // Get current user session
  getSession: protectedProcedure.query(async ({ ctx }) => {
    return ctx.session;
  }),

  // Get current user profile
  getProfile: protectedProcedure.query(async ({ ctx }) => {
    const profile = await ctx.db
      .select()
      .from(userProfiles)
      .where(eq(userProfiles.userId, ctx.session.user.id))
      .limit(1);

    if (profile.length === 0) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "User profile not found",
      });
    }

    return profile[0];
  }),

  // Update user profile
  updateProfile: protectedProcedure
    .input(
      z.object({
        fullName: z.string().min(2).optional(),
        schoolName: z.string().min(2).optional(),
        interests: z.string().optional(),
        dislikes: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const [updatedProfile] = await ctx.db
        .update(userProfiles)
        .set({
          ...input,
          updatedAt: new Date(),
        })
        .where(eq(userProfiles.userId, ctx.session.user.id))
        .returning();

      return updatedProfile;
    }),

  // Delete user account
  deleteAccount: protectedProcedure.mutation(async ({ ctx }) => {
    // Delete user (cascade will handle related records)
    await ctx.db.delete(users).where(eq(users.id, ctx.session.user.id));

    return { success: true };
  }),
});
