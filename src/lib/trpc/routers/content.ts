import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../init";
import { TRPCError } from "@trpc/server";
import { 
  subjects, 
  topics, 
  videos, 
  quizzes, 
  quizQuestions,
  userSubjectSelections,
  userTopicProgress 
} from "@/lib/db/schema";
import { eq, and, inArray } from "drizzle-orm";

export const contentRouter = createTRPCRouter({
  // Get subjects by standard and educational board
  getSubjectsByStandard: protectedProcedure
    .input(
      z.object({
        standard: z.enum(["8th", "9th", "10th"]),
        educationalBoard: z.enum(["CBSE", "ICSE", "State Board"]),
      })
    )
    .query(async ({ ctx, input }) => {
      return await ctx.db
        .select()
        .from(subjects)
        .where(
          and(
            eq(subjects.standard, input.standard),
            eq(subjects.educationalBoard, input.educationalBoard),
            eq(subjects.isActive, true)
          )
        )
        .orderBy(subjects.orderIndex, subjects.name);
    }),

  // Get user's selected subjects
  getUserSelectedSubjects: protectedProcedure.query(async ({ ctx }) => {
    return await ctx.db
      .select({
        id: subjects.id,
        name: subjects.name,
        description: subjects.description,
        orderIndex: subjects.orderIndex,
        selectedAt: userSubjectSelections.selectedAt,
      })
      .from(userSubjectSelections)
      .innerJoin(subjects, eq(userSubjectSelections.subjectId, subjects.id))
      .where(
        and(
          eq(userSubjectSelections.userId, ctx.session.user.id),
          eq(userSubjectSelections.isActive, true)
        )
      )
      .orderBy(subjects.orderIndex);
  }),

  // Select subjects for user
  selectSubjects: protectedProcedure
    .input(
      z.object({
        subjectIds: z.array(z.string().uuid()).min(1),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Verify all subjects exist
      const existingSubjects = await ctx.db
        .select()
        .from(subjects)
        .where(inArray(subjects.id, input.subjectIds));

      if (existingSubjects.length !== input.subjectIds.length) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "One or more subjects not found",
        });
      }

      // Remove existing selections
      await ctx.db
        .delete(userSubjectSelections)
        .where(eq(userSubjectSelections.userId, ctx.session.user.id));

      // Insert new selections
      const selections = input.subjectIds.map((subjectId) => ({
        userId: ctx.session.user.id,
        subjectId,
      }));

      await ctx.db.insert(userSubjectSelections).values(selections);

      return { success: true };
    }),

  // Get topics by subject with progress
  getTopicsBySubject: protectedProcedure
    .input(
      z.object({
        subjectId: z.string().uuid(),
      })
    )
    .query(async ({ ctx, input }) => {
      // Get topics with user progress
      const topicsWithProgress = await ctx.db
        .select({
          id: topics.id,
          name: topics.name,
          description: topics.description,
          orderIndex: topics.orderIndex,
          prerequisites: topics.prerequisites,
          estimatedDuration: topics.estimatedDuration,
          difficulty: topics.difficulty,
          progress: {
            status: userTopicProgress.status,
            progressPercentage: userTopicProgress.progressPercentage,
            completedAt: userTopicProgress.completedAt,
          },
        })
        .from(topics)
        .leftJoin(
          userTopicProgress,
          and(
            eq(userTopicProgress.topicId, topics.id),
            eq(userTopicProgress.userId, ctx.session.user.id)
          )
        )
        .where(
          and(
            eq(topics.subjectId, input.subjectId),
            eq(topics.isActive, true)
          )
        )
        .orderBy(topics.orderIndex);

      return topicsWithProgress;
    }),

  // Get videos by topic
  getVideosByTopic: protectedProcedure
    .input(
      z.object({
        topicId: z.string().uuid(),
      })
    )
    .query(async ({ ctx, input }) => {
      return await ctx.db
        .select()
        .from(videos)
        .where(
          and(
            eq(videos.topicId, input.topicId),
            eq(videos.isActive, true)
          )
        )
        .orderBy(videos.orderIndex);
    }),

  // Get quizzes by topic
  getQuizzesByTopic: protectedProcedure
    .input(
      z.object({
        topicId: z.string().uuid(),
      })
    )
    .query(async ({ ctx, input }) => {
      return await ctx.db
        .select()
        .from(quizzes)
        .where(
          and(
            eq(quizzes.topicId, input.topicId),
            eq(quizzes.isActive, true)
          )
        )
        .orderBy(quizzes.createdAt);
    }),

  // Get quiz questions
  getQuizQuestions: protectedProcedure
    .input(
      z.object({
        quizId: z.string().uuid(),
      })
    )
    .query(async ({ ctx, input }) => {
      return await ctx.db
        .select({
          id: quizQuestions.id,
          question: quizQuestions.question,
          type: quizQuestions.type,
          options: quizQuestions.options,
          points: quizQuestions.points,
          orderIndex: quizQuestions.orderIndex,
          // Don't return correct answer to client
        })
        .from(quizQuestions)
        .where(eq(quizQuestions.quizId, input.quizId))
        .orderBy(quizQuestions.orderIndex);
    }),

  // Get single topic details
  getTopicDetails: protectedProcedure
    .input(
      z.object({
        topicId: z.string().uuid(),
      })
    )
    .query(async ({ ctx, input }) => {
      const topic = await ctx.db
        .select()
        .from(topics)
        .where(eq(topics.id, input.topicId))
        .limit(1);

      if (topic.length === 0) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Topic not found",
        });
      }

      return topic[0];
    }),
});
