import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../init";
import { TRPCError } from "@trpc/server";
import { 
  userTopicProgress,
  userVideoProgress,
  userQuizAttempts,
  userNotes,
  quizQuestions,
  videos,
  topics
} from "@/lib/db/schema";
import { eq, and, desc, sql } from "drizzle-orm";

export const progressRouter = createTRPCRouter({
  // Update video progress
  updateVideoProgress: protectedProcedure
    .input(
      z.object({
        videoId: z.string().uuid(),
        watchTime: z.number().int().min(0),
        totalDuration: z.number().int().min(1),
        lastWatchedPosition: z.number().int().min(0),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const watchPercentage = Math.min((input.watchTime / input.totalDuration) * 100, 100);
      const completed = watchPercentage >= 90; // Consider 90% as completed

      // Upsert video progress
      const existingProgress = await ctx.db
        .select()
        .from(userVideoProgress)
        .where(
          and(
            eq(userVideoProgress.userId, ctx.session.user.id),
            eq(userVideoProgress.videoId, input.videoId)
          )
        )
        .limit(1);

      if (existingProgress.length > 0) {
        // Update existing progress
        const [updated] = await ctx.db
          .update(userVideoProgress)
          .set({
            watchTime: input.watchTime,
            totalDuration: input.totalDuration,
            watchPercentage,
            completed,
            lastWatchedPosition: input.lastWatchedPosition,
            lastWatchedAt: new Date(),
            updatedAt: new Date(),
          })
          .where(eq(userVideoProgress.id, existingProgress[0].id))
          .returning();

        return updated;
      } else {
        // Create new progress
        const [created] = await ctx.db
          .insert(userVideoProgress)
          .values({
            userId: ctx.session.user.id,
            videoId: input.videoId,
            watchTime: input.watchTime,
            totalDuration: input.totalDuration,
            watchPercentage,
            completed,
            lastWatchedPosition: input.lastWatchedPosition,
          })
          .returning();

        return created;
      }
    }),

  // Submit quiz attempt
  submitQuizAttempt: protectedProcedure
    .input(
      z.object({
        quizId: z.string().uuid(),
        answers: z.record(z.string(), z.string()), // questionId -> answer
        timeSpent: z.number().int().min(0).optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Get quiz questions with correct answers
      const questions = await ctx.db
        .select()
        .from(quizQuestions)
        .where(eq(quizQuestions.quizId, input.quizId));

      if (questions.length === 0) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Quiz questions not found",
        });
      }

      // Calculate score
      let correctAnswers = 0;
      for (const question of questions) {
        const userAnswer = input.answers[question.id];
        if (userAnswer && userAnswer.toLowerCase().trim() === question.correctAnswer.toLowerCase().trim()) {
          correctAnswers++;
        }
      }

      const score = Math.round((correctAnswers / questions.length) * 100);

      // Get attempt number
      const previousAttempts = await ctx.db
        .select({ count: sql<number>`count(*)` })
        .from(userQuizAttempts)
        .where(
          and(
            eq(userQuizAttempts.userId, ctx.session.user.id),
            eq(userQuizAttempts.quizId, input.quizId)
          )
        );

      const attemptNumber = (previousAttempts[0]?.count || 0) + 1;

      // Create quiz attempt
      const [attempt] = await ctx.db
        .insert(userQuizAttempts)
        .values({
          userId: ctx.session.user.id,
          quizId: input.quizId,
          score,
          totalQuestions: questions.length,
          correctAnswers,
          timeSpent: input.timeSpent,
          answers: JSON.stringify(input.answers),
          isPassed: score >= 70, // Default passing score
          attemptNumber,
        })
        .returning();

      return {
        ...attempt,
        answers: input.answers, // Return original answers object
      };
    }),

  // Create or update user note
  createNote: protectedProcedure
    .input(
      z.object({
        videoId: z.string().uuid(),
        content: z.string().min(1),
        timestamp: z.number().int().min(0).optional(),
        tags: z.array(z.string()).optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const [note] = await ctx.db
        .insert(userNotes)
        .values({
          userId: ctx.session.user.id,
          videoId: input.videoId,
          content: input.content,
          timestamp: input.timestamp,
          tags: input.tags ? JSON.stringify(input.tags) : null,
        })
        .returning();

      return note;
    }),

  // Get user notes for a video
  getNotesByVideo: protectedProcedure
    .input(
      z.object({
        videoId: z.string().uuid(),
      })
    )
    .query(async ({ ctx, input }) => {
      return await ctx.db
        .select()
        .from(userNotes)
        .where(
          and(
            eq(userNotes.userId, ctx.session.user.id),
            eq(userNotes.videoId, input.videoId)
          )
        )
        .orderBy(userNotes.timestamp, userNotes.createdAt);
    }),

  // Update note
  updateNote: protectedProcedure
    .input(
      z.object({
        noteId: z.string().uuid(),
        content: z.string().min(1),
        tags: z.array(z.string()).optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const [updated] = await ctx.db
        .update(userNotes)
        .set({
          content: input.content,
          tags: input.tags ? JSON.stringify(input.tags) : null,
          updatedAt: new Date(),
        })
        .where(
          and(
            eq(userNotes.id, input.noteId),
            eq(userNotes.userId, ctx.session.user.id)
          )
        )
        .returning();

      if (!updated) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Note not found",
        });
      }

      return updated;
    }),

  // Delete note
  deleteNote: protectedProcedure
    .input(
      z.object({
        noteId: z.string().uuid(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const deleted = await ctx.db
        .delete(userNotes)
        .where(
          and(
            eq(userNotes.id, input.noteId),
            eq(userNotes.userId, ctx.session.user.id)
          )
        )
        .returning();

      if (deleted.length === 0) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Note not found",
        });
      }

      return { success: true };
    }),

  // Get user's overall progress
  getUserProgress: protectedProcedure
    .input(
      z.object({
        subjectId: z.string().uuid().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      let query = ctx.db
        .select({
          topicId: userTopicProgress.topicId,
          status: userTopicProgress.status,
          progressPercentage: userTopicProgress.progressPercentage,
          completedAt: userTopicProgress.completedAt,
          topicName: topics.name,
          subjectId: topics.subjectId,
        })
        .from(userTopicProgress)
        .innerJoin(topics, eq(userTopicProgress.topicId, topics.id))
        .where(eq(userTopicProgress.userId, ctx.session.user.id));

      if (input.subjectId) {
        query = query.where(eq(topics.subjectId, input.subjectId));
      }

      return await query.orderBy(topics.orderIndex);
    }),

  // Get quiz attempts for a quiz
  getQuizAttempts: protectedProcedure
    .input(
      z.object({
        quizId: z.string().uuid(),
      })
    )
    .query(async ({ ctx, input }) => {
      return await ctx.db
        .select({
          id: userQuizAttempts.id,
          score: userQuizAttempts.score,
          totalQuestions: userQuizAttempts.totalQuestions,
          correctAnswers: userQuizAttempts.correctAnswers,
          timeSpent: userQuizAttempts.timeSpent,
          isPassed: userQuizAttempts.isPassed,
          attemptNumber: userQuizAttempts.attemptNumber,
          completedAt: userQuizAttempts.completedAt,
        })
        .from(userQuizAttempts)
        .where(
          and(
            eq(userQuizAttempts.userId, ctx.session.user.id),
            eq(userQuizAttempts.quizId, input.quizId)
          )
        )
        .orderBy(desc(userQuizAttempts.completedAt));
    }),
});
