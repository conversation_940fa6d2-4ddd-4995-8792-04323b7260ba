import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../init";
import { TRPCError } from "@trpc/server";
import { 
  userXp,
  userLevels,
  userBadges,
  userStreaks,
  badges,
  leaderboards,
  dailyChallenges,
  userDailyChallengeProgress
} from "@/lib/db/schema";
import { eq, and, desc, sql, gte, lte } from "drizzle-orm";

export const gamificationRouter = createTRPCRouter({
  // Get user's current stats
  getUserStats: protectedProcedure.query(async ({ ctx }) => {
    // Get user level and XP
    const userLevel = await ctx.db
      .select()
      .from(userLevels)
      .where(eq(userLevels.userId, ctx.session.user.id))
      .limit(1);

    // Get total XP from XP table
    const totalXpResult = await ctx.db
      .select({ total: sql<number>`sum(${userXp.amount})` })
      .from(userXp)
      .where(eq(userXp.userId, ctx.session.user.id));

    const totalXp = totalXpResult[0]?.total || 0;

    // Get current streaks
    const streaks = await ctx.db
      .select()
      .from(userStreaks)
      .where(
        and(
          eq(userStreaks.userId, ctx.session.user.id),
          eq(userStreaks.isActive, true)
        )
      );

    // Get badge count
    const badgeCount = await ctx.db
      .select({ count: sql<number>`count(*)` })
      .from(userBadges)
      .where(eq(userBadges.userId, ctx.session.user.id));

    return {
      level: userLevel[0] || { currentLevel: 1, totalXp: 0, xpToNextLevel: 100 },
      totalXp,
      streaks,
      badgeCount: badgeCount[0]?.count || 0,
    };
  }),

  // Award XP to user
  awardXp: protectedProcedure
    .input(
      z.object({
        amount: z.number().int().min(1),
        source: z.enum(["video_completion", "quiz_completion", "note_creation", "daily_login", "streak_milestone"]),
        sourceId: z.string().uuid().optional(),
        description: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Create XP record
      const [xpRecord] = await ctx.db
        .insert(userXp)
        .values({
          userId: ctx.session.user.id,
          amount: input.amount,
          source: input.source,
          sourceId: input.sourceId,
          description: input.description,
        })
        .returning();

      // Update user level
      const currentLevel = await ctx.db
        .select()
        .from(userLevels)
        .where(eq(userLevels.userId, ctx.session.user.id))
        .limit(1);

      const newTotalXp = (currentLevel[0]?.totalXp || 0) + input.amount;
      const newLevel = Math.floor(newTotalXp / 100) + 1; // Simple leveling formula
      const xpToNextLevel = (newLevel * 100) - newTotalXp;

      if (currentLevel.length > 0) {
        // Update existing level
        await ctx.db
          .update(userLevels)
          .set({
            totalXp: newTotalXp,
            currentLevel: newLevel,
            xpToNextLevel,
            levelUpAt: newLevel > currentLevel[0].currentLevel ? new Date() : currentLevel[0].levelUpAt,
            updatedAt: new Date(),
          })
          .where(eq(userLevels.userId, ctx.session.user.id));
      } else {
        // Create new level record
        await ctx.db
          .insert(userLevels)
          .values({
            userId: ctx.session.user.id,
            totalXp: newTotalXp,
            currentLevel: newLevel,
            xpToNextLevel,
            levelUpAt: newLevel > 1 ? new Date() : undefined,
          });
      }

      return xpRecord;
    }),

  // Update user streak
  updateStreak: protectedProcedure
    .input(
      z.object({
        streakType: z.enum(["daily_login", "topic_completion", "quiz_completion", "note_taking"]),
        increment: z.boolean().default(true),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const existingStreak = await ctx.db
        .select()
        .from(userStreaks)
        .where(
          and(
            eq(userStreaks.userId, ctx.session.user.id),
            eq(userStreaks.streakType, input.streakType)
          )
        )
        .limit(1);

      if (existingStreak.length > 0) {
        const streak = existingStreak[0];
        const newCount = input.increment ? streak.currentCount + 1 : 0;
        const newMaxCount = Math.max(streak.maxCount, newCount);

        const [updated] = await ctx.db
          .update(userStreaks)
          .set({
            currentCount: newCount,
            maxCount: newMaxCount,
            lastActivity: new Date(),
            isActive: newCount > 0,
            updatedAt: new Date(),
          })
          .where(eq(userStreaks.id, streak.id))
          .returning();

        return updated;
      } else {
        // Create new streak
        const [created] = await ctx.db
          .insert(userStreaks)
          .values({
            userId: ctx.session.user.id,
            streakType: input.streakType,
            currentCount: input.increment ? 1 : 0,
            maxCount: input.increment ? 1 : 0,
            isActive: input.increment,
          })
          .returning();

        return created;
      }
    }),

  // Get user badges
  getUserBadges: protectedProcedure.query(async ({ ctx }) => {
    return await ctx.db
      .select({
        id: userBadges.id,
        earnedAt: userBadges.earnedAt,
        progress: userBadges.progress,
        isDisplayed: userBadges.isDisplayed,
        badge: {
          id: badges.id,
          name: badges.name,
          description: badges.description,
          category: badges.category,
          iconUrl: badges.iconUrl,
          rarity: badges.rarity,
        },
      })
      .from(userBadges)
      .innerJoin(badges, eq(userBadges.badgeId, badges.id))
      .where(eq(userBadges.userId, ctx.session.user.id))
      .orderBy(desc(userBadges.earnedAt));
  }),

  // Award badge to user
  awardBadge: protectedProcedure
    .input(
      z.object({
        badgeId: z.string().uuid(),
        progress: z.number().int().min(0).optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Check if user already has this badge
      const existingBadge = await ctx.db
        .select()
        .from(userBadges)
        .where(
          and(
            eq(userBadges.userId, ctx.session.user.id),
            eq(userBadges.badgeId, input.badgeId)
          )
        )
        .limit(1);

      if (existingBadge.length > 0) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "User already has this badge",
        });
      }

      // Award the badge
      const [userBadge] = await ctx.db
        .insert(userBadges)
        .values({
          userId: ctx.session.user.id,
          badgeId: input.badgeId,
          progress: input.progress || 0,
        })
        .returning();

      return userBadge;
    }),

  // Get leaderboard
  getLeaderboard: protectedProcedure
    .input(
      z.object({
        type: z.enum(["weekly_xp", "monthly_xp", "streak_count", "quiz_average"]),
        period: z.enum(["weekly", "monthly", "all_time"]).default("weekly"),
        limit: z.number().int().min(1).max(100).default(10),
      })
    )
    .query(async ({ ctx, input }) => {
      // For now, return a simple XP-based leaderboard
      // In production, this would be more sophisticated with proper period filtering
      const leaderboardData = await ctx.db
        .select({
          userId: userLevels.userId,
          totalXp: userLevels.totalXp,
          currentLevel: userLevels.currentLevel,
        })
        .from(userLevels)
        .orderBy(desc(userLevels.totalXp))
        .limit(input.limit);

      return leaderboardData.map((entry, index) => ({
        rank: index + 1,
        ...entry,
      }));
    }),

  // Get available badges
  getAvailableBadges: protectedProcedure.query(async ({ ctx }) => {
    return await ctx.db
      .select()
      .from(badges)
      .where(eq(badges.isActive, true))
      .orderBy(badges.category, badges.name);
  }),

  // Get XP history
  getXpHistory: protectedProcedure
    .input(
      z.object({
        limit: z.number().int().min(1).max(100).default(20),
      })
    )
    .query(async ({ ctx, input }) => {
      return await ctx.db
        .select()
        .from(userXp)
        .where(eq(userXp.userId, ctx.session.user.id))
        .orderBy(desc(userXp.earnedAt))
        .limit(input.limit);
    }),
});
