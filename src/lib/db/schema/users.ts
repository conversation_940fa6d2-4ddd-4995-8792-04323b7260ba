import { pgTable, text, timestamp, uuid, varchar, boolean } from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { z } from "zod";

// Users table - Core authentication data
export const users = pgTable("users", {
  id: uuid("id").primaryKey().defaultRandom(),
  email: varchar("email", { length: 255 }).unique().notNull(),
  phone: varchar("phone", { length: 20 }).unique(),
  passwordHash: text("password_hash").notNull(),
  emailVerified: boolean("email_verified").default(false),
  phoneVerified: boolean("phone_verified").default(false),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// User profiles table - Extended user information
export const userProfiles = pgTable("user_profiles", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id").references(() => users.id, { onDelete: "cascade" }).notNull(),
  fullName: varchar("full_name", { length: 255 }).notNull(),
  standard: varchar("standard", { length: 10 }).notNull(), // 8th, 9th, 10th
  schoolName: varchar("school_name", { length: 255 }).notNull(),
  educationalBoard: varchar("educational_board", { length: 50 }).notNull(), // CBSE, ICSE, State Boards
  interests: text("interests"), // JSON array of interests
  dislikes: text("dislikes"), // JSON array of dislikes
  profileCompleted: boolean("profile_completed").default(false),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Zod schemas for validation
export const insertUserSchema = createInsertSchema(users, {
  email: z.string().email(),
  phone: z.string().optional(),
  passwordHash: z.string().min(8),
});

export const selectUserSchema = createSelectSchema(users);

export const insertUserProfileSchema = createInsertSchema(userProfiles, {
  fullName: z.string().min(2).max(255),
  standard: z.enum(["8th", "9th", "10th"]),
  schoolName: z.string().min(2).max(255),
  educationalBoard: z.enum(["CBSE", "ICSE", "State Board"]),
  interests: z.string().optional(),
  dislikes: z.string().optional(),
});

export const selectUserProfileSchema = createSelectSchema(userProfiles);

// Types
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
export type UserProfile = typeof userProfiles.$inferSelect;
export type NewUserProfile = typeof userProfiles.$inferInsert;
