import { pgTable, text, timestamp, uuid, varchar, integer, boolean } from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { z } from "zod";
import { users } from "./users";

// Badges master table - Available badges in the system
export const badges = pgTable("badges", {
  id: uuid("id").primaryKey().defaultRandom(),
  name: varchar("name", { length: 100 }).notNull(),
  description: text("description").notNull(),
  category: varchar("category", { length: 50 }).notNull(), // completion, performance, engagement
  iconUrl: text("icon_url"),
  criteria: text("criteria"), // JSON object defining earning criteria
  xpReward: integer("xp_reward").default(0),
  rarity: varchar("rarity", { length: 20 }).default("common"), // common, rare, epic, legendary
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// User badges - Badges earned by users
export const userBadges = pgTable("user_badges", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id").references(() => users.id, { onDelete: "cascade" }).notNull(),
  badgeId: uuid("badge_id").references(() => badges.id, { onDelete: "cascade" }).notNull(),
  earnedAt: timestamp("earned_at").defaultNow().notNull(),
  progress: integer("progress").default(0), // for progressive badges
  isDisplayed: boolean("is_displayed").default(true), // user can choose to display or hide
});

// User streaks - Different types of streaks
export const userStreaks = pgTable("user_streaks", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id").references(() => users.id, { onDelete: "cascade" }).notNull(),
  streakType: varchar("streak_type", { length: 50 }).notNull(), // daily_login, topic_completion, quiz_completion, note_taking
  currentCount: integer("current_count").default(0),
  maxCount: integer("max_count").default(0), // best streak ever
  lastActivity: timestamp("last_activity").defaultNow(),
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// User XP tracking - Experience points and sources
export const userXp = pgTable("user_xp", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id").references(() => users.id, { onDelete: "cascade" }).notNull(),
  amount: integer("amount").notNull(),
  source: varchar("source", { length: 50 }).notNull(), // video_completion, quiz_completion, note_creation, daily_login, streak_milestone
  sourceId: uuid("source_id"), // ID of the related entity (video, quiz, etc.)
  description: text("description"),
  earnedAt: timestamp("earned_at").defaultNow().notNull(),
});

// User levels - Current level and progress
export const userLevels = pgTable("user_levels", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id").references(() => users.id, { onDelete: "cascade" }).unique().notNull(),
  currentLevel: integer("current_level").default(1),
  totalXp: integer("total_xp").default(0),
  xpToNextLevel: integer("xp_to_next_level").default(100),
  levelUpAt: timestamp("level_up_at"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Leaderboards - Different leaderboard types
export const leaderboards = pgTable("leaderboards", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id").references(() => users.id, { onDelete: "cascade" }).notNull(),
  leaderboardType: varchar("leaderboard_type", { length: 50 }).notNull(), // weekly_xp, monthly_xp, streak_count, quiz_average
  score: integer("score").notNull(),
  rank: integer("rank"),
  period: varchar("period", { length: 20 }).notNull(), // weekly, monthly, all_time
  periodStart: timestamp("period_start").notNull(),
  periodEnd: timestamp("period_end").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Daily challenges - Gamified daily tasks
export const dailyChallenges = pgTable("daily_challenges", {
  id: uuid("id").primaryKey().defaultRandom(),
  name: varchar("name", { length: 100 }).notNull(),
  description: text("description").notNull(),
  type: varchar("type", { length: 50 }).notNull(), // watch_videos, complete_quiz, take_notes, login_streak
  target: integer("target").notNull(), // target count to complete challenge
  xpReward: integer("xp_reward").default(10),
  validDate: timestamp("valid_date").notNull(), // date when challenge is valid
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// User daily challenge progress
export const userDailyChallengeProgress = pgTable("user_daily_challenge_progress", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id").references(() => users.id, { onDelete: "cascade" }).notNull(),
  challengeId: uuid("challenge_id").references(() => dailyChallenges.id, { onDelete: "cascade" }).notNull(),
  progress: integer("progress").default(0),
  completed: boolean("completed").default(false),
  completedAt: timestamp("completed_at"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Zod schemas
export const insertBadgeSchema = createInsertSchema(badges, {
  name: z.string().min(1).max(100),
  description: z.string().min(1),
  category: z.enum(["completion", "performance", "engagement"]),
  xpReward: z.number().int().min(0).optional(),
  rarity: z.enum(["common", "rare", "epic", "legendary"]).optional(),
});

export const selectBadgeSchema = createSelectSchema(badges);

export const insertUserBadgeSchema = createInsertSchema(userBadges, {
  progress: z.number().int().min(0).optional(),
});

export const selectUserBadgeSchema = createSelectSchema(userBadges);

export const insertUserStreakSchema = createInsertSchema(userStreaks, {
  streakType: z.enum(["daily_login", "topic_completion", "quiz_completion", "note_taking"]),
  currentCount: z.number().int().min(0).optional(),
  maxCount: z.number().int().min(0).optional(),
});

export const selectUserStreakSchema = createSelectSchema(userStreaks);

export const insertUserXpSchema = createInsertSchema(userXp, {
  amount: z.number().int().min(1),
  source: z.enum(["video_completion", "quiz_completion", "note_creation", "daily_login", "streak_milestone"]),
});

export const selectUserXpSchema = createSelectSchema(userXp);

export const insertUserLevelSchema = createInsertSchema(userLevels, {
  currentLevel: z.number().int().min(1).optional(),
  totalXp: z.number().int().min(0).optional(),
  xpToNextLevel: z.number().int().min(0).optional(),
});

export const selectUserLevelSchema = createSelectSchema(userLevels);

// Types
export type Badge = typeof badges.$inferSelect;
export type NewBadge = typeof badges.$inferInsert;
export type UserBadge = typeof userBadges.$inferSelect;
export type NewUserBadge = typeof userBadges.$inferInsert;
export type UserStreak = typeof userStreaks.$inferSelect;
export type NewUserStreak = typeof userStreaks.$inferInsert;
export type UserXp = typeof userXp.$inferSelect;
export type NewUserXp = typeof userXp.$inferInsert;
export type UserLevel = typeof userLevels.$inferSelect;
export type NewUserLevel = typeof userLevels.$inferInsert;
export type Leaderboard = typeof leaderboards.$inferSelect;
export type NewLeaderboard = typeof leaderboards.$inferInsert;
export type DailyChallenge = typeof dailyChallenges.$inferSelect;
export type NewDailyChallenge = typeof dailyChallenges.$inferInsert;
export type UserDailyChallengeProgress = typeof userDailyChallengeProgress.$inferSelect;
export type NewUserDailyChallengeProgress = typeof userDailyChallengeProgress.$inferInsert;
