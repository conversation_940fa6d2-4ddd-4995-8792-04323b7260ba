import {
  pgTable,
  text,
  timestamp,
  uuid,
  varchar,
  boolean,
} from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { z } from "zod";
import { users } from "./users";

// Available pets table - Master data for pet types
export const pets = pgTable("pets", {
  id: uuid("id").primaryKey().defaultRandom(),
  name: varchar("name", { length: 100 }).notNull(), // <PERSON>, <PERSON>, Turtle, Panda, Mouse, Rabbit
  type: varchar("type", { length: 50 }).notNull(),
  imageUrl: text("image_url").notNull(),
  description: text("description"),
  personality: text("personality"), // JSON object for AI personality traits
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// User selected pets table - Links users to their chosen pets
export const userPets = pgTable("user_pets", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id")
    .references(() => users.id, { onDelete: "cascade" })
    .notNull(),
  petId: uuid("pet_id")
    .references(() => pets.id, { onDelete: "cascade" })
    .notNull(),
  petName: varchar("pet_name", { length: 100 }).notNull(), // User-given name
  selectedAt: timestamp("selected_at").defaultNow().notNull(),
  isActive: boolean("is_active").default(true), // For future pet switching feature
});

// Zod schemas for validation
export const insertPetSchema = createInsertSchema(pets, {
  name: z.string().min(1).max(100),
  type: z.enum(["Cat", "Dog", "Turtle", "Panda", "Mouse", "Rabbit"]),
  imageUrl: z.string().url(),
  description: z.string().optional(),
  personality: z.string().optional(),
});

export const selectPetSchema = createSelectSchema(pets);

export const insertUserPetSchema = createInsertSchema(userPets, {
  petName: z.string().min(1).max(100),
});

export const selectUserPetSchema = createSelectSchema(userPets);

// Types
export type Pet = typeof pets.$inferSelect;
export type NewPet = typeof pets.$inferInsert;
export type UserPet = typeof userPets.$inferSelect;
export type NewUserPet = typeof userPets.$inferInsert;
