// Export all tables and schemas
export * from "./users";
export * from "./pets";
export * from "./content";
export * from "./progress";
export * from "./gamification";

// Re-export commonly used types for convenience
export type {
  User,
  NewUser,
  UserProfile,
  NewUserProfile,
} from "./users";

export type {
  Pet,
  NewPet,
  UserPet,
  NewUserPet,
} from "./pets";

export type {
  Subject,
  NewSubject,
  Topic,
  NewTopic,
  Video,
  NewVideo,
  Quiz,
  NewQuiz,
  QuizQuestion,
  NewQuizQuestion,
} from "./content";

export type {
  UserTopicProgress,
  NewUserTopicProgress,
  UserVideoProgress,
  NewUserVideoProgress,
  UserQuizAttempt,
  NewUserQuizAttempt,
  UserNote,
  NewUserNote,
  UserSubjectSelection,
  NewUserSubjectSelection,
} from "./progress";

export type {
  Badge,
  NewBadge,
  UserBadge,
  NewUserBadge,
  UserStreak,
  NewUserStreak,
  UserXp,
  NewUserXp,
  UserLevel,
  NewUserLevel,
  Leaderboard,
  NewLeaderboard,
  DailyChallenge,
  NewDailyChallenge,
  UserDailyChallengeProgress,
  NewUserDailyChallengeProgress,
} from "./gamification";
