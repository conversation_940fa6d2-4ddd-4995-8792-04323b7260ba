import {
  pgTable,
  text,
  timestamp,
  uuid,
  varchar,
  integer,
  boolean,
  real,
} from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { z } from "zod";
import { users } from "./users";
import { topics, videos, quizzes, subjects } from "./content";

// User topic progress - Overall progress per topic
export const userTopicProgress = pgTable("user_topic_progress", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id")
    .references(() => users.id, { onDelete: "cascade" })
    .notNull(),
  topicId: uuid("topic_id")
    .references(() => topics.id, { onDelete: "cascade" })
    .notNull(),
  status: varchar("status", { length: 20 }).default("not_started"), // not_started, in_progress, completed
  progressPercentage: integer("progress_percentage").default(0), // 0-100
  startedAt: timestamp("started_at"),
  completedAt: timestamp("completed_at"),
  lastAccessedAt: timestamp("last_accessed_at").defaultNow(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// User video progress - Detailed video watching progress
export const userVideoProgress = pgTable("user_video_progress", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id")
    .references(() => users.id, { onDelete: "cascade" })
    .notNull(),
  videoId: uuid("video_id")
    .references(() => videos.id, { onDelete: "cascade" })
    .notNull(),
  watchTime: integer("watch_time").default(0), // seconds watched
  totalDuration: integer("total_duration"), // total video duration
  completed: boolean("completed").default(false),
  watchPercentage: real("watch_percentage").default(0), // 0-100
  lastWatchedPosition: integer("last_watched_position").default(0), // resume position
  lastWatchedAt: timestamp("last_watched_at").defaultNow(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// User quiz attempts - Quiz performance tracking
export const userQuizAttempts = pgTable("user_quiz_attempts", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id")
    .references(() => users.id, { onDelete: "cascade" })
    .notNull(),
  quizId: uuid("quiz_id")
    .references(() => quizzes.id, { onDelete: "cascade" })
    .notNull(),
  score: integer("score").notNull(), // percentage score
  totalQuestions: integer("total_questions").notNull(),
  correctAnswers: integer("correct_answers").notNull(),
  timeSpent: integer("time_spent"), // seconds
  answers: text("answers"), // JSON object with question_id: answer pairs
  isPassed: boolean("is_passed").default(false),
  attemptNumber: integer("attempt_number").default(1),
  completedAt: timestamp("completed_at").defaultNow().notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// User notes - Student notes linked to videos
export const userNotes = pgTable("user_notes", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id")
    .references(() => users.id, { onDelete: "cascade" })
    .notNull(),
  videoId: uuid("video_id")
    .references(() => videos.id, { onDelete: "cascade" })
    .notNull(),
  content: text("content").notNull(),
  timestamp: integer("timestamp"), // video timestamp in seconds
  isPrivate: boolean("is_private").default(true),
  tags: text("tags"), // JSON array of tags
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// User subject selections - Tracks which subjects user has selected
export const userSubjectSelections = pgTable("user_subject_selections", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id")
    .references(() => users.id, { onDelete: "cascade" })
    .notNull(),
  subjectId: uuid("subject_id")
    .references(() => subjects.id, { onDelete: "cascade" })
    .notNull(),
  selectedAt: timestamp("selected_at").defaultNow().notNull(),
  isActive: boolean("is_active").default(true),
});

// Zod schemas
export const insertUserTopicProgressSchema = createInsertSchema(
  userTopicProgress,
  {
    status: z.enum(["not_started", "in_progress", "completed"]).optional(),
    progressPercentage: z.number().int().min(0).max(100).optional(),
  }
);

export const selectUserTopicProgressSchema =
  createSelectSchema(userTopicProgress);

export const insertUserVideoProgressSchema = createInsertSchema(
  userVideoProgress,
  {
    watchTime: z.number().int().min(0).optional(),
    watchPercentage: z.number().min(0).max(100).optional(),
    lastWatchedPosition: z.number().int().min(0).optional(),
  }
);

export const selectUserVideoProgressSchema =
  createSelectSchema(userVideoProgress);

export const insertUserQuizAttemptSchema = createInsertSchema(
  userQuizAttempts,
  {
    score: z.number().int().min(0).max(100),
    totalQuestions: z.number().int().min(1),
    correctAnswers: z.number().int().min(0),
    timeSpent: z.number().int().min(0).optional(),
    attemptNumber: z.number().int().min(1).optional(),
  }
);

export const selectUserQuizAttemptSchema = createSelectSchema(userQuizAttempts);

export const insertUserNoteSchema = createInsertSchema(userNotes, {
  content: z.string().min(1),
  timestamp: z.number().int().min(0).optional(),
});

export const selectUserNoteSchema = createSelectSchema(userNotes);

export const insertUserSubjectSelectionSchema = createInsertSchema(
  userSubjectSelections
);
export const selectUserSubjectSelectionSchema = createSelectSchema(
  userSubjectSelections
);

// Types
export type UserTopicProgress = typeof userTopicProgress.$inferSelect;
export type NewUserTopicProgress = typeof userTopicProgress.$inferInsert;
export type UserVideoProgress = typeof userVideoProgress.$inferSelect;
export type NewUserVideoProgress = typeof userVideoProgress.$inferInsert;
export type UserQuizAttempt = typeof userQuizAttempts.$inferSelect;
export type NewUserQuizAttempt = typeof userQuizAttempts.$inferInsert;
export type UserNote = typeof userNotes.$inferSelect;
export type NewUserNote = typeof userNotes.$inferInsert;
export type UserSubjectSelection = typeof userSubjectSelections.$inferSelect;
export type NewUserSubjectSelection = typeof userSubjectSelections.$inferInsert;
