import { pgTable, text, timestamp, uuid, varchar, integer, boolean } from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { z } from "zod";

// Subjects table - Academic subjects by standard and board
export const subjects = pgTable("subjects", {
  id: uuid("id").primaryKey().defaultRandom(),
  name: varchar("name", { length: 100 }).notNull(), // Mathematics, Physics, etc.
  standard: varchar("standard", { length: 10 }).notNull(), // 8th, 9th, 10th
  educationalBoard: varchar("educational_board", { length: 50 }).notNull(),
  description: text("description"),
  orderIndex: integer("order_index").default(0),
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Topics table - Individual learning topics within subjects
export const topics = pgTable("topics", {
  id: uuid("id").primaryKey().defaultRandom(),
  subjectId: uuid("subject_id").references(() => subjects.id, { onDelete: "cascade" }).notNull(),
  name: varchar("name", { length: 200 }).notNull(),
  description: text("description"),
  orderIndex: integer("order_index").default(0),
  prerequisites: text("prerequisites"), // JSON array of prerequisite topic IDs
  estimatedDuration: integer("estimated_duration"), // in minutes
  difficulty: varchar("difficulty", { length: 20 }).default("medium"), // easy, medium, hard
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Videos table - Learning videos for topics
export const videos = pgTable("videos", {
  id: uuid("id").primaryKey().defaultRandom(),
  topicId: uuid("topic_id").references(() => topics.id, { onDelete: "cascade" }).notNull(),
  title: varchar("title", { length: 255 }).notNull(),
  description: text("description"),
  url: text("url").notNull(), // Cloudflare R2 URL
  duration: integer("duration"), // in seconds
  orderIndex: integer("order_index").default(0),
  thumbnailUrl: text("thumbnail_url"),
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Quizzes table - Assessments for topics
export const quizzes = pgTable("quizzes", {
  id: uuid("id").primaryKey().defaultRandom(),
  topicId: uuid("topic_id").references(() => topics.id, { onDelete: "cascade" }).notNull(),
  title: varchar("title", { length: 255 }).notNull(),
  description: text("description"),
  passingScore: integer("passing_score").default(70), // percentage
  timeLimit: integer("time_limit"), // in minutes, null for unlimited
  maxAttempts: integer("max_attempts").default(3),
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Quiz questions table
export const quizQuestions = pgTable("quiz_questions", {
  id: uuid("id").primaryKey().defaultRandom(),
  quizId: uuid("quiz_id").references(() => quizzes.id, { onDelete: "cascade" }).notNull(),
  question: text("question").notNull(),
  type: varchar("type", { length: 50 }).notNull(), // MCQ, fill-in-blanks, drag-and-drop
  options: text("options"), // JSON array for MCQ options
  correctAnswer: text("correct_answer").notNull(),
  explanation: text("explanation"),
  points: integer("points").default(1),
  orderIndex: integer("order_index").default(0),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Zod schemas
export const insertSubjectSchema = createInsertSchema(subjects, {
  name: z.string().min(1).max(100),
  standard: z.enum(["8th", "9th", "10th"]),
  educationalBoard: z.enum(["CBSE", "ICSE", "State Board"]),
  orderIndex: z.number().int().min(0).optional(),
});

export const selectSubjectSchema = createSelectSchema(subjects);

export const insertTopicSchema = createInsertSchema(topics, {
  name: z.string().min(1).max(200),
  orderIndex: z.number().int().min(0).optional(),
  difficulty: z.enum(["easy", "medium", "hard"]).optional(),
  estimatedDuration: z.number().int().min(1).optional(),
});

export const selectTopicSchema = createSelectSchema(topics);

export const insertVideoSchema = createInsertSchema(videos, {
  title: z.string().min(1).max(255),
  url: z.string().url(),
  duration: z.number().int().min(1).optional(),
  orderIndex: z.number().int().min(0).optional(),
});

export const selectVideoSchema = createSelectSchema(videos);

export const insertQuizSchema = createInsertSchema(quizzes, {
  title: z.string().min(1).max(255),
  passingScore: z.number().int().min(0).max(100).optional(),
  timeLimit: z.number().int().min(1).optional(),
  maxAttempts: z.number().int().min(1).optional(),
});

export const selectQuizSchema = createSelectSchema(quizzes);

export const insertQuizQuestionSchema = createInsertSchema(quizQuestions, {
  question: z.string().min(1),
  type: z.enum(["MCQ", "fill-in-blanks", "drag-and-drop"]),
  correctAnswer: z.string().min(1),
  points: z.number().int().min(1).optional(),
  orderIndex: z.number().int().min(0).optional(),
});

export const selectQuizQuestionSchema = createSelectSchema(quizQuestions);

// Types
export type Subject = typeof subjects.$inferSelect;
export type NewSubject = typeof subjects.$inferInsert;
export type Topic = typeof topics.$inferSelect;
export type NewTopic = typeof topics.$inferInsert;
export type Video = typeof videos.$inferSelect;
export type NewVideo = typeof videos.$inferInsert;
export type Quiz = typeof quizzes.$inferSelect;
export type NewQuiz = typeof quizzes.$inferInsert;
export type QuizQuestion = typeof quizQuestions.$inferSelect;
export type NewQuizQuestion = typeof quizQuestions.$inferInsert;
