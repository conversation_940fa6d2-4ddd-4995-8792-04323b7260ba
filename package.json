{"name": "code", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "format": "biome format ./src --write", "check": "biome check --write ./src", "check!": "biome check --write --unsafe --no-errors-on-unmatched ./src", "clean": "rm -rf .next .output node_modules && pnpm install && pnpm dev", "build:check": "pnpm check! && pnpm build", "prepare": "husky"}, "dependencies": {"@neondatabase/serverless": "^1.0.1", "@radix-ui/react-slot": "^1.2.3", "@t3-oss/env-nextjs": "^0.13.8", "@tanstack/react-query": "^5.81.5", "@trpc/client": "^11.4.3", "@trpc/next": "^11.4.3", "@trpc/react-query": "^11.4.3", "@trpc/server": "^11.4.3", "@types/bcryptjs": "^3.0.0", "@types/pg": "^8.15.4", "bcryptjs": "^3.0.2", "better-auth": "^1.2.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.2", "drizzle-zod": "^0.8.2", "lucide-react": "^0.525.0", "next": "15.3.4", "next-themes": "^0.4.6", "pg": "^8.16.3", "react": "^19.0.0", "react-dom": "^19.0.0", "resend": "^4.6.0", "sonner": "^2.0.5", "superjson": "^2.2.2", "tailwind-merge": "^3.3.1", "zod": "^3.25.67"}, "devDependencies": {"@biomejs/biome": "2.0.6", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "husky": "^9.1.7", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}